// This should only be used in development and testing environments.
// It will store the functions that won't be used in production.
use crate::Candle;

use chrono::{DateTime, Utc, NaiveDateTime};
use polars::{prelude::*,
            frame::row::Row
};
use std::fs::File;

pub fn get_data() -> Result<DataFrame, PolarsError> {
    // Try CSV first, fallback to <PERSON><PERSON><PERSON> for backward compatibility
    if std::path::Path::new("data/egx_data.csv").exists() {
        get_csv_data()
    } else if std::path::Path::new("data/EURUSD.parquet").exists() {
        get_parquet_data()
    } else {
        Err(PolarsError::IO {
            error: std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "No data file found. Please provide either 'data/egx_data.csv' or 'data/EURUSD.parquet'"
            ).into(),
            msg: Some("Data file not found".into())
        })
    }
}

pub fn get_csv_data() -> Result<DataFrame, PolarsError> {
    // Read CSV file using standard library and create DataFrame manually
    use std::io::{BufRead, BufReader};

    let file = File::open("data/egx_data.csv")
        .map_err(|e| PolarsError::IO { error: e.into(), msg: Some("Failed to open CSV file".into()) })?;

    let reader = BufReader::new(file);
    let mut lines = reader.lines();

    // Skip header line
    let _header = lines.next();

    let mut symbols = Vec::new();
    let mut datetimes = Vec::new();
    let mut opens = Vec::new();
    let mut highs = Vec::new();
    let mut lows = Vec::new();
    let mut closes = Vec::new();
    let mut volumes = Vec::new();

    for line in lines {
        let line = line.map_err(|e| PolarsError::IO { error: e.into(), msg: Some("Failed to read line".into()) })?;
        let parts: Vec<&str> = line.split(',').collect();

        if parts.len() >= 7 {
            symbols.push(parts[0].to_string());
            datetimes.push(parts[1].to_string());
            opens.push(parts[2].parse::<f64>().unwrap_or(0.0));
            highs.push(parts[3].parse::<f64>().unwrap_or(0.0));
            lows.push(parts[4].parse::<f64>().unwrap_or(0.0));
            closes.push(parts[5].parse::<f64>().unwrap_or(0.0));
            volumes.push(parts[6].parse::<i64>().unwrap_or(0));
        }
    }

    // Create DataFrame
    df! {
        "symbol" => symbols,
        "datetime" => datetimes,
        "open" => opens,
        "high" => highs,
        "low" => lows,
        "close" => closes,
        "volume" => volumes,
    }
}

pub fn get_parquet_data() -> Result<DataFrame, PolarsError> {
    ParquetReader::new(File::open("data/EURUSD.parquet")?)
        .finish()
}

// Parse candle data from row - supports both CSV and Parquet formats
// Expected CSV format: symbol,datetime,open,high,low,close,volume
// Expected Parquet format: datetime,open,high,low,close,volume
pub fn parse_candle(row: Row) -> Result<Candle, String> {
    // Determine format based on row length
    let (symbol, datetime, open, high, low, close, volume) = if row.0.len() == 7 {
        // CSV format with symbol column
        parse_csv_row(row)?
    } else if row.0.len() == 6 {
        // Parquet format without symbol column
        parse_parquet_row(row)?
    } else {
        return Err(format!("Invalid row format. Expected 6 or 7 columns, got {}", row.0.len()));
    };

    Ok(Candle {
        symbol,
        timerange: "1m".to_string(),  // Base timeframe
        timestamp: datetime,
        open,
        high,
        low,
        close,
        volume,
    })
}

fn parse_csv_row(row: Row) -> Result<(String, DateTime<Utc>, f64, f64, f64, f64, f64), String> {
    // CSV format: symbol,datetime,open,high,low,close,volume
    let symbol = match &row.0[0] {
        AnyValue::String(s) => s.to_string(),
        AnyValue::StringOwned(s) => s.to_string(),
        _ => return Err("Invalid 'symbol' value in CSV row".to_string()),
    };

    let datetime = parse_datetime_value(&row.0[1])?;
    let open = parse_float_value(&row.0[2], "open")?;
    let high = parse_float_value(&row.0[3], "high")?;
    let low = parse_float_value(&row.0[4], "low")?;
    let close = parse_float_value(&row.0[5], "close")?;
    let volume = parse_volume_value(&row.0[6])?;

    Ok((symbol, datetime, open, high, low, close, volume))
}

fn parse_parquet_row(row: Row) -> Result<(String, DateTime<Utc>, f64, f64, f64, f64, f64), String> {
    // Parquet format: datetime,open,high,low,close,volume
    let datetime = parse_datetime_value(&row.0[0])?;
    let open = parse_float_value(&row.0[1], "open")?;
    let high = parse_float_value(&row.0[2], "high")?;
    let low = parse_float_value(&row.0[3], "low")?;
    let close = parse_float_value(&row.0[4], "close")?;
    let volume = parse_volume_value(&row.0[5])?;

    Ok(("EURUSD".to_string(), datetime, open, high, low, close, volume))
}

fn parse_datetime_value(value: &AnyValue) -> Result<DateTime<Utc>, String> {
    match value {
        AnyValue::Datetime(ts, TimeUnit::Microseconds, _) => {
            DateTime::from_timestamp_micros(*ts)
                .map(|dt| dt.with_timezone(&Utc))
                .ok_or("Invalid timestamp".to_string())
        },
        AnyValue::Datetime(ts, TimeUnit::Milliseconds, _) => {
            DateTime::from_timestamp_millis(*ts)
                .map(|dt| dt.with_timezone(&Utc))
                .ok_or("Invalid timestamp".to_string())
        },
        AnyValue::String(s) => {
            // Try parsing common datetime formats
            parse_datetime_string(s)
        },
        AnyValue::StringOwned(s) => {
            // Try parsing common datetime formats
            parse_datetime_string(s)
        },
        _ => Err("Invalid datetime format".to_string()),
    }
}

fn parse_datetime_string(s: &str) -> Result<DateTime<Utc>, String> {
    // Try multiple datetime formats commonly used in CSV files
    let formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S%.f",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%S%.f",
        "%Y-%m-%dT%H:%M:%SZ",
        "%Y-%m-%dT%H:%M:%S%.fZ",
        "%d/%m/%Y %H:%M:%S",
        "%m/%d/%Y %H:%M:%S",
    ];

    for format in &formats {
        if let Ok(naive_dt) = NaiveDateTime::parse_from_str(s, format) {
            return Ok(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
        }
    }

    Err(format!("Unable to parse datetime: {}", s))
}

fn parse_float_value(value: &AnyValue, field_name: &str) -> Result<f64, String> {
    match value {
        AnyValue::Float64(val) => Ok(*val),
        AnyValue::Float32(val) => Ok(*val as f64),
        AnyValue::Int64(val) => Ok(*val as f64),
        AnyValue::Int32(val) => Ok(*val as f64),
        AnyValue::String(s) => {
            s.parse::<f64>().map_err(|_| format!("Invalid {} value: {}", field_name, s))
        },
        AnyValue::StringOwned(s) => {
            s.parse::<f64>().map_err(|_| format!("Invalid {} value: {}", field_name, s))
        },
        _ => Err(format!("Invalid {} value type", field_name)),
    }
}

fn parse_volume_value(value: &AnyValue) -> Result<f64, String> {
    match value {
        AnyValue::Int64(val) => Ok(*val as f64),
        AnyValue::Int32(val) => Ok(*val as f64),
        AnyValue::Float64(val) => Ok(*val),
        AnyValue::Float32(val) => Ok(*val as f64),
        AnyValue::String(s) => {
            s.parse::<f64>().map_err(|_| format!("Invalid volume value: {}", s))
        },
        AnyValue::StringOwned(s) => {
            s.parse::<f64>().map_err(|_| format!("Invalid volume value: {}", s))
        },
        _ => Err("Invalid volume value type".to_string()),
    }
}