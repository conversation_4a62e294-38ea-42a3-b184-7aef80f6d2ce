[package]
name = "paragon"
version = "0.1.0"
edition = "2021"

[dependencies]
chrono = "0.4.41"
dashmap = "6.1.0"
futures = "0.3.31"
once_cell = "1.21.3"
polars = { version = "0.48.1", features = ["csv", "parquet", "timezones"] }
tokio = {version = "1.45.1" , features = ["full"] }
# Removed PostgreSQL dependencies - using CSV files instead
# deadpool-postgres = "0.14.1"
# tokio-postgres = { version = "0.7.13", features = ["with-chrono-0_4"] }
