use paragon::{
    handlers::candle::aggregate_candle,
    TIMERANGES,
    utils::temporary,
};

use futures::future::join_all;
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), String> {
    println!("🧠 Paragon - EGX Smart Money Concept Analyzer");
    println!("============================================");

    // Load the data
    println!("📊 Loading market data...");
    let data = temporary::get_data().map_err(|e| e.to_string())?;
    println!("✅ Loaded {} candles", data.height());

    let mut processed_count = 0;
    let total_count = data.height();

    // Iterate over each row in the data
    for index in 0..data.height() {
        let row = data.get_row(index).map_err(|e| e.to_string())?;
        let parsed_candle = temporary::parse_candle(row).map_err(|e| e.to_string())?;

        let candle = Arc::new(parsed_candle);

        // Spawn a task for each timerange to aggregate the candle
        let mut handles = Vec::new();
        for timerange in TIMERANGES.iter() {
            let cloned_candle = Arc::clone(&candle);
            let symbol = candle.symbol.clone();
            let task = tokio::spawn(async move {
                aggregate_candle(cloned_candle, &symbol, timerange).await
            });

            handles.push(task);
        }

        // Wait for all tasks to complete
        let _ = join_all(handles).await;

        processed_count += 1;
        if processed_count % 1000 == 0 || processed_count == total_count {
            println!("⚡ Processed {}/{} candles ({:.1}%)",
                processed_count, total_count,
                (processed_count as f64 / total_count as f64) * 100.0);
        }
    }

    println!("🎯 Processing complete! Aggregated candles for all timeframes.");
    println!("📈 Ready for SMC pattern detection...");

    Ok(())
}
